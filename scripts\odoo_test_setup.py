#!/usr/bin/env python3
"""
Script pour configurer et tester le module dans Odoo
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

class OdooTestSetup:
    def __init__(self):
        self.module_name = 'gantt_textile'
        self.odoo_path = os.environ.get('ODOO_PATH', '/opt/odoo')
        self.addons_path = os.environ.get('ADDONS_PATH', '/opt/odoo/addons')
        
    def setup_module(self):
        """Copier le module dans le répertoire addons d'Odoo"""
        print("📁 Configuration du module dans Odoo...")
        
        current_dir = Path(__file__).parent.parent
        target_dir = Path(self.addons_path) / self.module_name
        
        try:
            if target_dir.exists():
                shutil.rmtree(target_dir)
            
            shutil.copytree(current_dir, target_dir)
            print(f"✅ Module copié vers: {target_dir}")
            
        except Exception as e:
            print(f"❌ Erreur lors de la copie: {e}")
            return False
        
        return True
    
    def install_module(self):
        """Installer le module dans Odoo"""
        print("📦 Installation du module dans Odoo...")
        
        try:
            # Commande d'installation Odoo
            cmd = [
                'python3', f'{self.odoo_path}/odoo-bin',
                '-d', 'test_gantt_db',
                '-i', self.module_name,
                '--stop-after-init'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ Module installé avec succès")
                return True
            else:
                print("❌ Erreur lors de l'installation:")
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ Erreur: {e}")
            return False
    
    def run_odoo_tests(self):
        """Exécuter les tests Odoo"""
        print("🧪 Exécution des tests Odoo...")
        
        try:
            cmd = [
                'python3', f'{self.odoo_path}/odoo-bin',
                '-d', 'test_gantt_db',
                '--test-enable',
                '--test-tags', self.module_name,
                '--stop-after-init'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if "FAILED" not in result.stderr:
                print("✅ Tests Odoo: PASSED")
                return True
            else:
                print("❌ Tests Odoo: FAILED")
                print(result.stderr)
                return False
                
        except Exception as e:
            print(f"❌ Erreur tests Odoo: {e}")
            return False
    
    def full_setup_and_test(self):
        """Configuration complète et tests"""
        print("🚀 Configuration complète du module Gantt Textile")
        print("=" * 60)
        
        steps = [
            ("Configuration du module", self.setup_module),
            ("Installation dans Odoo", self.install_module),
            ("Exécution des tests", self.run_odoo_tests)
        ]
        
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}...")
            if not step_func():
                print(f"❌ Échec à l'étape: {step_name}")
                return False
        
        print("\n🎉 Configuration et tests terminés avec succès!")
        return True

if __name__ == '__main__':
    setup = OdooTestSetup()
    setup.full_setup_and_test()
