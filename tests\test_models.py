import unittest
from unittest.mock import Mock, patch
from datetime import datetime, timedelta
import sys
import os

# Ajouter le chemin du module pour les tests unitaires
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

class TestProjectGanttModels(unittest.TestCase):
    """Tests unitaires pour les modèles sans dépendance Odoo"""
    
    def setUp(self):
        """Configuration initiale pour chaque test"""
        self.mock_project_data = {
            'id': 1,
            'name': 'Test Textile Project',
            'textile_type': 'cotton',
            'production_capacity': 100.0,
            'quality_grade': 'a'
        }
        
        self.mock_task_data = {
            'id': 1,
            'name': 'Test Task',
            'date_start': datetime.now(),
            'date_end': datetime.now() + timedelta(days=5),
            'progress': 50.0,
            'textile_process': 'spinning'
        }
    
    def test_gantt_duration_calculation(self):
        """Test du calcul de durée Gantt"""
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 10)
        
        # Simulation du calcul de durée
        duration = (end_date - start_date).days
        
        self.assertEqual(duration, 9)
        print("✅ Test durée Gantt: PASSED")
    
    def test_progress_calculation(self):
        """Test du calcul de progression"""
        tasks_progress = [25.0, 50.0, 75.0, 100.0]
        
        # Simulation du calcul de progression moyenne
        average_progress = sum(tasks_progress) / len(tasks_progress)
        
        self.assertEqual(average_progress, 62.5)
        print("✅ Test calcul progression: PASSED")
    
    def test_textile_type_validation(self):
        """Test de validation des types textiles"""
        valid_types = ['cotton', 'silk', 'wool', 'synthetic', 'blend']
        test_type = 'cotton'
        
        self.assertIn(test_type, valid_types)
        print("✅ Test validation type textile: PASSED")
    
    def test_gantt_data_structure(self):
        """Test de la structure des données Gantt"""
        expected_structure = {
            'projects': [],
            'tasks': [],
            'links': []
        }
        
        # Simulation de la structure de données
        gantt_data = {
            'projects': [self.mock_project_data],
            'tasks': [self.mock_task_data],
            'links': []
        }
        
        self.assertIn('projects', gantt_data)
        self.assertIn('tasks', gantt_data)
        self.assertIn('links', gantt_data)
        print("✅ Test structure données Gantt: PASSED")

if __name__ == '__main__':
    print("🧪 Démarrage des tests unitaires Python...")
    unittest.main(verbosity=2)
