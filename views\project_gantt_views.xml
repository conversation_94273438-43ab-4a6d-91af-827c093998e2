<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Project Gantt View -->
    <record id="view_project_gantt_chart" model="ir.ui.view">
        <field name="name">project.project.gantt</field>
        <field name="model">project.project</field>
        <field name="arch" type="xml">
            <gantt date_start="gantt_start_date" 
                   date_stop="gantt_end_date"
                   progress="gantt_progress"
                   default_group_by="user_id"
                   decoration-success="gantt_progress &gt;= 100"
                   decoration-warning="gantt_progress &gt;= 50 and gantt_progress &lt; 100"
                   decoration-danger="gantt_progress &lt; 50">
                <field name="name"/>
                <field name="user_id"/>
                <field name="textile_type"/>
                <field name="stage_id"/>
                <field name="gantt_duration"/>
            </gantt>
        </field>
    </record>

    <!-- Enhanced Project Form View -->
    <record id="view_project_form_gantt" model="ir.ui.view">
        <field name="name">project.project.form.gantt</field>
        <field name="model">project.project</field>
        <field name="inherit_id" ref="project.edit_project"/>
        <field name="arch" type="xml">
            <xpath expr="//sheet" position="inside">
                <group name="textile_info" string="Textile Information">
                    <field name="textile_type"/>
                    <field name="production_capacity"/>
                    <field name="quality_grade"/>
                </group>
                <group name="gantt_info" string="Gantt Information">
                    <field name="gantt_start_date" readonly="1"/>
                    <field name="gantt_end_date" readonly="1"/>
                    <field name="gantt_progress" readonly="1"/>
                    <field name="gantt_duration" readonly="1"/>
                </group>
            </xpath>
        </field>
    </record>

    <!-- Interactive Gantt Chart View -->
    <record id="view_project_interactive_gantt" model="ir.ui.view">
        <field name="name">project.interactive.gantt</field>
        <field name="model">project.project</field>
        <field name="arch" type="xml">
            <form string="Interactive Gantt Chart">
                <header>
                    <button name="export_gantt_csv" string="Export CSV" type="object" class="btn-primary"/>
                    <button name="%(action_gantt_pdf_report)d" string="Export PDF" type="action" class="btn-secondary"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>Interactive Gantt Chart</h1>
                    </div>
                    <div id="gantt_container" class="o_gantt_container">
                        <!-- Gantt chart will be rendered here by JavaScript -->
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Task Form View Enhancement -->
    <record id="view_task_form_gantt" model="ir.ui.view">
        <field name="name">project.task.form.gantt</field>
        <field name="model">project.task</field>
        <field name="inherit_id" ref="project.view_task_form2"/>
        <field name="arch" type="xml">
            <xpath expr="//sheet" position="inside">
                <group name="textile_task_info" string="Textile Process Information">
                    <field name="textile_process"/>
                    <field name="machine_required"/>
                    <field name="progress"/>
                    <field name="estimated_hours"/>
                    <field name="actual_hours" readonly="1"/>
                </group>
                <group name="quality_info" string="Quality Information">
                    <field name="quality_parameters"/>
                </group>
            </xpath>
        </field>
    </record>

    <!-- Project List View with Gantt Fields -->
    <record id="view_project_tree_gantt" model="ir.ui.view">
        <field name="name">project.project.tree.gantt</field>
        <field name="model">project.project</field>
        <field name="inherit_id" ref="project.view_project"/>
        <field name="arch" type="xml">
            <xpath expr="//tree" position="attributes">
                <attribute name="decoration-success">gantt_progress >= 100</attribute>
                <attribute name="decoration-warning">gantt_progress >= 50 and gantt_progress &lt; 100</attribute>
                <attribute name="decoration-danger">gantt_progress &lt; 50</attribute>
            </xpath>
            <field name="user_id" position="after">
                <field name="textile_type"/>
                <field name="gantt_progress" widget="progressbar"/>
                <field name="gantt_duration"/>
            </field>
        </field>
    </record>
</odoo>
