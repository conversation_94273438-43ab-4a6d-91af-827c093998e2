from odoo import models, fields, api

class ProjectTaskGantt(models.Model):
    _inherit = 'project.task'
    
    # Enhanced fields for Gantt functionality
    progress = fields.Float(string='Progress (%)', default=0.0)
    estimated_hours = fields.Float(string='Estimated Hours')
    actual_hours = fields.Float(string='Actual Hours', compute='_compute_actual_hours')
    
    # Textile-specific task fields
    textile_process = fields.Selection([
        ('spinning', 'Spinning'),
        ('weaving', 'Weaving'),
        ('dyeing', 'Dyeing'),
        ('finishing', 'Finishing'),
        ('quality_check', 'Quality Check'),
        ('packaging', 'Packaging')
    ], string='Textile Process')
    
    machine_required = fields.Char(string='Machine Required')
    quality_parameters = fields.Text(string='Quality Parameters')
    
    @api.depends('timesheet_ids.unit_amount')
    def _compute_actual_hours(self):
        for task in self:
            task.actual_hours = sum(task.timesheet_ids.mapped('unit_amount'))
    
    def update_progress(self, progress_value):
        """Update task progress"""
        self.progress = progress_value
        return True
