from odoo import http
from odoo.http import request
import json
import base64
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib.styles import getSampleStyleSheet
import io

class GanttController(http.Controller):
    
    @http.route('/gantt/data', type='json', auth='user')
    def get_gantt_data(self, project_ids=None):
        """Get Gantt chart data"""
        domain = []
        if project_ids:
            domain = [('id', 'in', project_ids)]
        
        projects = request.env['project.project'].search(domain)
        return projects.get_gantt_data()
    
    @http.route('/gantt/update_task', type='json', auth='user')
    def update_task(self, task_id, start_date=None, end_date=None, progress=None):
        """Update task data from Gantt chart"""
        task = request.env['project.task'].browse(int(task_id.replace('task_', '')))
        
        vals = {}
        if start_date:
            vals['date_start'] = start_date
        if end_date:
            vals['date_end'] = end_date
        if progress is not None:
            vals['progress'] = progress * 100
        
        if vals:
            task.write(vals)
        
        return {'success': True}
    
    @http.route('/gantt/export/pdf', type='http', auth='user')
    def export_gantt_pdf(self, project_ids=None):
        """Export Gantt chart to PDF"""
        if project_ids:
            project_ids = json.loads(project_ids)
            projects = request.env['project.project'].browse(project_ids)
        else:
            projects = request.env['project.project'].search([])
        
        # Create PDF
        buffer = io.BytesIO()
        doc = SimpleDocTemplate(buffer, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title = Paragraph("Textile Project Gantt Chart Report", styles['Title'])
        story.append(title)
        
        # Create table data
        data = [['Project/Task', 'Start Date', 'End Date', 'Progress', 'Responsible', 'Status']]
        
        for project in projects:
            data.append([
                project.name,
                project.gantt_start_date.strftime('%Y-%m-%d') if project.gantt_start_date else '',
                project.gantt_end_date.strftime('%Y-%m-%d') if project.gantt_end_date else '',
                f'{project.gantt_progress:.1f}%',
                project.user_id.name if project.user_id else '',
                project.stage_id.name if project.stage_id else 'Draft'
            ])
            
            for task in project.task_ids:
                data.append([
                    f'  └─ {task.name}',
                    task.date_start.strftime('%Y-%m-%d') if task.date_start else '',
                    task.date_end.strftime('%Y-%m-%d') if task.date_end else '',
                    f'{task.progress:.1f}%',
                    task.user_ids[0].name if task.user_ids else '',
                    task.stage_id.name if task.stage_id else 'New'
                ])
        
        # Create table
        table = Table(data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        doc.build(story)
        
        pdf_data = buffer.getvalue()
        buffer.close()
        
        return request.make_response(
            pdf_data,
            headers=[
                ('Content-Type', 'application/pdf'),
                ('Content-Disposition', 'attachment; filename="gantt_chart.pdf"')
            ]
        )
    
    @http.route('/gantt/export/csv', type='http', auth='user')
    def export_gantt_csv(self, project_ids=None):
        """Export Gantt chart to CSV"""
        if project_ids:
            project_ids = json.loads(project_ids)
            projects = request.env['project.project'].browse(project_ids)
        else:
            projects = request.env['project.project'].search([])
        
        return projects.export_gantt_csv()
