from odoo import models, fields, api
from datetime import datetime, timedelta
import json

class ProjectGantt(models.Model):
    _inherit = 'project.project'
    
    # Textile-specific fields
    textile_type = fields.Selection([
        ('cotton', 'Cotton'),
        ('silk', 'Silk'),
        ('wool', 'Wool'),
        ('synthetic', 'Synthetic'),
        ('blend', 'Blend')
    ], string='Textile Type')
    
    production_capacity = fields.Float(string='Production Capacity (units/day)')
    quality_grade = fields.Selection([
        ('a', 'Grade A'),
        ('b', 'Grade B'),
        ('c', 'Grade C')
    ], string='Quality Grade')
    
    # Gantt-specific fields
    gantt_start_date = fields.Datetime(string='Gantt Start Date', compute='_compute_gantt_dates', store=True)
    gantt_end_date = fields.Datetime(string='Gantt End Date', compute='_compute_gantt_dates', store=True)
    gantt_progress = fields.Float(string='Overall Progress (%)', compute='_compute_gantt_progress')
    gantt_duration = fields.Integer(string='Duration (Days)', compute='_compute_gantt_duration')
    
    @api.depends('task_ids.date_start', 'task_ids.date_end')
    def _compute_gantt_dates(self):
        for project in self:
            if project.task_ids:
                start_dates = project.task_ids.mapped('date_start')
                end_dates = project.task_ids.mapped('date_end')
                
                project.gantt_start_date = min([d for d in start_dates if d]) if any(start_dates) else False
                project.gantt_end_date = max([d for d in end_dates if d]) if any(end_dates) else False
            else:
                project.gantt_start_date = False
                project.gantt_end_date = False
    
    @api.depends('task_ids.progress')
    def _compute_gantt_progress(self):
        for project in self:
            if project.task_ids:
                total_progress = sum(project.task_ids.mapped('progress'))
                project.gantt_progress = total_progress / len(project.task_ids) if project.task_ids else 0
            else:
                project.gantt_progress = 0
    
    @api.depends('gantt_start_date', 'gantt_end_date')
    def _compute_gantt_duration(self):
        for project in self:
            if project.gantt_start_date and project.gantt_end_date:
                delta = project.gantt_end_date - project.gantt_start_date
                project.gantt_duration = delta.days
            else:
                project.gantt_duration = 0
    
    def get_gantt_data(self):
        """Return formatted data for Gantt chart"""
        gantt_data = {
            'projects': [],
            'tasks': [],
            'links': []
        }
        
        for project in self:
            project_data = {
                'id': f'project_{project.id}',
                'text': project.name,
                'start_date': project.gantt_start_date.strftime('%Y-%m-%d') if project.gantt_start_date else '',
                'end_date': project.gantt_end_date.strftime('%Y-%m-%d') if project.gantt_end_date else '',
                'progress': project.gantt_progress / 100,
                'type': 'project',
                'responsible': project.user_id.name if project.user_id else '',
                'textile_type': dict(project._fields['textile_type'].selection).get(project.textile_type, ''),
                'status': project.stage_id.name if project.stage_id else 'Draft'
            }
            gantt_data['projects'].append(project_data)
            
            for task in project.task_ids:
                task_data = {
                    'id': f'task_{task.id}',
                    'text': task.name,
                    'start_date': task.date_start.strftime('%Y-%m-%d') if task.date_start else '',
                    'end_date': task.date_end.strftime('%Y-%m-%d') if task.date_end else '',
                    'progress': task.progress / 100,
                    'parent': f'project_{project.id}',
                    'type': 'task',
                    'responsible': task.user_ids[0].name if task.user_ids else '',
                    'status': task.stage_id.name if task.stage_id else 'New'
                }
                gantt_data['tasks'].append(task_data)
        
        return gantt_data
    
    def export_gantt_csv(self):
        """Export Gantt data to CSV"""
        import csv
        import io
        import base64
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # Write headers
        writer.writerow([
            'Type', 'Name', 'Start Date', 'End Date', 'Progress (%)', 
            'Responsible', 'Status', 'Textile Type', 'Duration (Days)'
        ])
        
        for project in self:
            writer.writerow([
                'Project',
                project.name,
                project.gantt_start_date.strftime('%Y-%m-%d') if project.gantt_start_date else '',
                project.gantt_end_date.strftime('%Y-%m-%d') if project.gantt_end_date else '',
                f'{project.gantt_progress:.1f}',
                project.user_id.name if project.user_id else '',
                project.stage_id.name if project.stage_id else 'Draft',
                dict(project._fields['textile_type'].selection).get(project.textile_type, ''),
                project.gantt_duration
            ])
            
            for task in project.task_ids:
                writer.writerow([
                    'Task',
                    task.name,
                    task.date_start.strftime('%Y-%m-%d') if task.date_start else '',
                    task.date_end.strftime('%Y-%m-%d') if task.date_end else '',
                    f'{task.progress:.1f}',
                    task.user_ids[0].name if task.user_ids else '',
                    task.stage_id.name if task.stage_id else 'New',
                    '',
                    (task.date_end - task.date_start).days if task.date_start and task.date_end else 0
                ])
        
        csv_data = output.getvalue()
        output.close()
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'data:text/csv;base64,{base64.b64encode(csv_data.encode()).decode()}',
            'target': 'self',
        }
