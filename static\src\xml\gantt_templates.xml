<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="gantt_textile.GanttChartTemplate" owl="1">
        <div class="o_gantt_widget">
            <div class="o_gantt_header">
                <div class="o_gantt_controls">
                    <button class="btn btn-primary" t-on-click="exportToPDF">
                        <i class="fa fa-file-pdf-o"/> Export PDF
                    </button>
                    <button class="btn btn-secondary" t-on-click="exportToCSV">
                        <i class="fa fa-file-excel-o"/> Export CSV
                    </button>
                </div>
            </div>
            <div class="o_gantt_container" t-ref="ganttContainer">
                <div t-if="state.loading" class="text-center p-4">
                    <i class="fa fa-spinner fa-spin fa-2x"/>
                    <p>Loading Gantt Chart...</p>
                </div>
            </div>
        </div>
    </t>
</templates>
