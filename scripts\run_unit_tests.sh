#!/bin/bash

echo "🧪 Script de Tests Unitaires - Module Gantt Textile"
echo "=================================================="

# Couleurs pour l'affichage
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}1. Tests Python Unitaires${NC}"
echo "----------------------------"

# Installer les dépendances de test
pip install -r requirements_test.txt

# Exécuter les tests Python
python -m pytest tests/test_models.py -v
python -m pytest tests/test_controllers.py -v

echo -e "\n${YELLOW}2. Tests JavaScript${NC}"
echo "----------------------"

# Vérifier si Node.js est installé
if command -v node &> /dev/null; then
    echo "Node.js détecté, exécution des tests JS..."
    # npm test (si configuré)
    node static/tests/test_gantt_widget.js
else
    echo "⚠️ Node.js non installé - Tests JS ignorés"
fi

echo -e "\n${GREEN}✅ Tests unitaires terminés!${NC}"
