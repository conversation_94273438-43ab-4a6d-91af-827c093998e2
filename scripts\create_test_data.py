#!/usr/bin/env python3
"""
Script pour créer des données de test
"""

import sys
import os
import random
from datetime import datetime, timedelta

# Simuler la création de données de test
def create_test_projects(count=10):
    """Créer des projets de test"""
    print(f"🏭 Création de {count} projets de test...")
    
    textile_types = ['cotton', 'silk', 'wool', 'synthetic', 'blend']
    quality_grades = ['a', 'b', 'c']
    
    projects = []
    
    for i in range(count):
        project = {
            'name': f'Projet Textile {i+1}',
            'textile_type': random.choice(textile_types),
            'production_capacity': random.randint(50, 500),
            'quality_grade': random.choice(quality_grades)
        }
        projects.append(project)
        print(f"  ✅ Projet {i+1}: {project['name']}")
    
    return projects

def create_test_tasks(project_count=10, tasks_per_project=5):
    """Créer des tâches de test"""
    print(f"📋 Création de {project_count * tasks_per_project} tâches de test...")
    
    processes = ['spinning', 'weaving', 'dyeing', 'finishing', 'quality_check', 'packaging']
    
    tasks = []
    
    for project_id in range(1, project_count + 1):
        for task_id in range(tasks_per_project):
            start_date = datetime.now() + timedelta(days=random.randint(0, 30))
            end_date = start_date + timedelta(days=random.randint(1, 10))
            
            task = {
                'name': f'Tâche {task_id + 1} - Projet {project_id}',
                'project_id': project_id,
                'textile_process': random.choice(processes),
                'date_start': start_date,
                'date_end': end_date,
                'progress': random.randint(0, 100)
            }
            tasks.append(task)
    
    print(f"  ✅ {len(tasks)} tâches créées")
    return tasks

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Créer des données de test')
    parser.add_argument('--projects', type=int, default=10, help='Nombre de projets')
    parser.add_argument('--tasks', type=int, default=50, help='Nombre de tâches')
    
    args = parser.parse_args()
    
    print("🧪 Génération de données de test...")
    print("=" * 40)
    
    projects = create_test_projects(args.projects)
    tasks = create_test_tasks(args.projects, args.tasks // args.projects)
    
    print("\n✅ Données de test générées!")
    print(f"📊 {len(projects)} projets, {len(tasks)} tâches")
