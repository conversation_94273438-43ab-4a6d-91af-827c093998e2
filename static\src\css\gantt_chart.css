.o_gantt_container {
  width: 100%;
  height: 600px;
  overflow: auto;
  border: 1px solid #ddd;
  background: #fff;
}

.gantt-wrapper {
  min-width: 1200px;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.gantt-timeline-header {
  background: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  position: sticky;
  top: 0;
  z-index: 10;
}

.gantt-months {
  display: flex;
  height: 40px;
  align-items: center;
  padding-left: 300px; /* Space for row info */
}

.gantt-month {
  min-width: 120px;
  text-align: center;
  font-weight: bold;
  border-right: 1px solid #dee2e6;
  padding: 8px;
}

.gantt-row {
  display: flex;
  min-height: 50px;
  border-bottom: 1px solid #e9ecef;
  align-items: center;
}

.gantt-project-row {
  background: #f8f9fa;
  font-weight: bold;
}

.gantt-task-row {
  background: #fff;
  margin-left: 20px;
}

.gantt-row-info {
  width: 300px;
  padding: 8px 12px;
  border-right: 1px solid #dee2e6;
  flex-shrink: 0;
}

.gantt-row-title {
  font-size: 14px;
  margin-bottom: 4px;
}

.task-title {
  font-size: 13px;
  color: #6c757d;
}

.gantt-row-details {
  font-size: 11px;
  color: #6c757d;
}

.gantt-row-details span {
  margin-right: 8px;
  padding: 2px 6px;
  background: #e9ecef;
  border-radius: 3px;
}

.gantt-timeline {
  flex: 1;
  position: relative;
  height: 50px;
  background: linear-gradient(90deg, transparent 119px, #dee2e6 120px);
  background-size: 120px 100%;
}

.gantt-bar {
  position: absolute;
  height: 24px;
  top: 13px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  overflow: hidden;
}

.project-bar {
  background: #007bff;
  border: 2px solid #0056b3;
}

.task-bar {
  background: #28a745;
  border: 2px solid #1e7e34;
}

.gantt-bar:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.gantt-progress {
  height: 100%;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.3s ease;
}

.gantt-bar-label {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 11px;
  font-weight: bold;
  text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.5);
}

.gantt-tooltip {
  position: absolute;
  background: #333;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.gantt-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #333;
}

/* Responsive design */
@media (max-width: 768px) {
  .gantt-row-info {
    width: 200px;
  }

  .gantt-months {
    padding-left: 200px;
  }

  .gantt-month {
    min-width: 80px;
    font-size: 12px;
  }
}

/* Status-based styling */
.gantt-row[data-status="done"] .gantt-bar {
  background: #28a745;
}

.gantt-row[data-status="in-progress"] .gantt-bar {
  background: #ffc107;
}

.gantt-row[data-status="blocked"] .gantt-bar {
  background: #dc3545;
}

/* Print styles */
@media print {
  .o_gantt_container {
    height: auto;
    overflow: visible;
  }

  .gantt-wrapper {
    min-width: auto;
  }

  .gantt-bar:hover {
    transform: none;
    box-shadow: none;
  }
}
