import unittest
from unittest.mock import Mock, patch
import json
import io

class TestGanttControllers(unittest.TestCase):
    """Tests pour les contrôleurs sans dépendance Odoo"""
    
    def setUp(self):
        """Configuration des mocks"""
        self.mock_request = Mock()
        self.mock_env = Mock()
        
    def test_csv_export_format(self):
        """Test du format d'export CSV"""
        # Données de test
        test_data = [
            ['Project', 'Test Project', '2024-01-01', '2024-01-10', '50.0', '<PERSON>', 'In Progress'],
            ['Task', 'Test Task', '2024-01-02', '2024-01-05', '75.0', '<PERSON>', 'Done']
        ]
        
        # Simulation de génération CSV
        output = io.StringIO()
        import csv
        writer = csv.writer(output)
        
        # Headers
        writer.writerow(['Type', 'Name', 'Start Date', 'End Date', 'Progress', 'Responsible', 'Status'])
        
        # Data
        for row in test_data:
            writer.writerow(row)
        
        csv_content = output.getvalue()
        output.close()
        
        self.assertIn('Test Project', csv_content)
        self.assertIn('Test Task', csv_content)
        print("✅ Test format export CSV: PASSED")
    
    def test_json_response_structure(self):
        """Test de la structure de réponse JSON"""
        mock_response = {
            'success': True,
            'data': {
                'projects': [],
                'tasks': []
            }
        }
        
        self.assertTrue(mock_response['success'])
        self.assertIn('data', mock_response)
        print("✅ Test structure réponse JSON: PASSED")

if __name__ == '__main__':
    print("🧪 Démarrage des tests contrôleurs...")
    unittest.main(verbosity=2)
