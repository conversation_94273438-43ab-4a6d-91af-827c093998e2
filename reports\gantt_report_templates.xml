<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="gantt_chart_template">
        <t t-call="web.html_container">
            <t t-foreach="docs" t-as="project">
                <t t-call="web.external_layout">
                    <div class="page">
                        <div class="oe_structure"/>
                        <div class="row">
                            <div class="col-12">
                                <h2>Textile Project Gantt Chart</h2>
                                <h3 t-field="project.name"/>
                            </div>
                        </div>
                        
                        <div class="row mt32">
                            <div class="col-6">
                                <strong>Project Information:</strong>
                                <table class="table table-sm">
                                    <tr>
                                        <td>Textile Type:</td>
                                        <td t-field="project.textile_type"/>
                                    </tr>
                                    <tr>
                                        <td>Responsible:</td>
                                        <td t-field="project.user_id"/>
                                    </tr>
                                    <tr>
                                        <td>Start Date:</td>
                                        <td t-field="project.gantt_start_date"/>
                                    </tr>
                                    <tr>
                                        <td>End Date:</td>
                                        <td t-field="project.gantt_end_date"/>
                                    </tr>
                                    <tr>
                                        <td>Duration:</td>
                                        <td><span t-field="project.gantt_duration"/> days</td>
                                    </tr>
                                    <tr>
                                        <td>Progress:</td>
                                        <td><span t-field="project.gantt_progress"/>%</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <div class="row mt32">
                            <div class="col-12">
                                <h4>Tasks</h4>
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Task Name</th>
                                            <th>Process</th>
                                            <th>Start Date</th>
                                            <th>End Date</th>
                                            <th>Progress</th>
                                            <th>Responsible</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <t t-foreach="project.task_ids" t-as="task">
                                            <tr>
                                                <td t-field="task.name"/>
                                                <td t-field="task.textile_process"/>
                                                <td t-field="task.date_start"/>
                                                <td t-field="task.date_end"/>
                                                <td><span t-field="task.progress"/>%</td>
                                                <td>
                                                    <span t-if="task.user_ids" t-field="task.user_ids[0]"/>
                                                </td>
                                            </tr>
                                        </t>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </t>
            </t>
        </t>
    </template>
</odoo>
