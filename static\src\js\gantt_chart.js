"use client"

/** @odoo-module **/

import { Component, onMounted, useRef, useState } from "@odoo/owl"
import { registry } from "@web/core/registry"
import { useService } from "@web/core/utils/hooks"

export class GanttChartWidget extends Component {
  setup() {
    this.rpc = useService("rpc")
    this.ganttRef = useRef("ganttContainer")
    this.state = useState({
      ganttData: null,
      loading: true,
    })

    // Ensure all hooks are called at the top level
    // No conditional calls for hooks here

    onMounted(() => {
      this.loadGanttChart()
    })
  }

  async loadGanttChart() {
    try {
      const data = await this.rpc("/gantt/data", {})
      this.state.ganttData = data
      this.state.loading = false
      this.renderGanttChart(data)
    } catch (error) {
      console.error("Error loading Gantt data:", error)
      this.state.loading = false
    }
  }

  renderGanttChart(data) {
    const container = this.ganttRef.el
    if (!container) return

    // Clear existing content
    container.innerHTML = ""

    // Create Gantt chart structure
    const ganttWrapper = document.createElement("div")
    ganttWrapper.className = "gantt-wrapper"

    // Create timeline header
    const timelineHeader = this.createTimelineHeader(data)
    ganttWrapper.appendChild(timelineHeader)

    // Create project rows
    data.projects.forEach((project) => {
      const projectRow = this.createProjectRow(project)
      ganttWrapper.appendChild(projectRow)

      // Add task rows
      const projectTasks = data.tasks.filter((task) => task.parent === project.id)
      projectTasks.forEach((task) => {
        const taskRow = this.createTaskRow(task)
        ganttWrapper.appendChild(taskRow)
      })
    })

    container.appendChild(ganttWrapper)
    this.addInteractivity()
  }

  createTimelineHeader(data) {
    const header = document.createElement("div")
    header.className = "gantt-timeline-header"

    // Calculate date range
    const allDates = [...data.projects, ...data.tasks]
      .filter((item) => item.start_date && item.end_date)
      .flatMap((item) => [new Date(item.start_date), new Date(item.end_date)])

    if (allDates.length === 0) return header

    const minDate = new Date(Math.min(...allDates))
    const maxDate = new Date(Math.max(...allDates))

    // Create month headers
    const monthsContainer = document.createElement("div")
    monthsContainer.className = "gantt-months"

    const currentDate = new Date(minDate.getFullYear(), minDate.getMonth(), 1)
    while (currentDate <= maxDate) {
      const monthDiv = document.createElement("div")
      monthDiv.className = "gantt-month"
      monthDiv.textContent = currentDate.toLocaleDateString("en-US", {
        month: "short",
        year: "numeric",
      })
      monthsContainer.appendChild(monthDiv)

      currentDate.setMonth(currentDate.getMonth() + 1)
    }

    header.appendChild(monthsContainer)
    return header
  }

  createProjectRow(project) {
    const row = document.createElement("div")
    row.className = "gantt-row gantt-project-row"
    row.dataset.id = project.id

    const info = document.createElement("div")
    info.className = "gantt-row-info"
    info.innerHTML = `
            <div class="gantt-row-title">${project.text}</div>
            <div class="gantt-row-details">
                <span class="responsible">${project.responsible}</span>
                <span class="status">${project.status}</span>
                <span class="textile-type">${project.textile_type}</span>
            </div>
        `

    const timeline = document.createElement("div")
    timeline.className = "gantt-timeline"

    if (project.start_date && project.end_date) {
      const bar = this.createGanttBar(project)
      timeline.appendChild(bar)
    }

    row.appendChild(info)
    row.appendChild(timeline)

    return row
  }

  createTaskRow(task) {
    const row = document.createElement("div")
    row.className = "gantt-row gantt-task-row"
    row.dataset.id = task.id

    const info = document.createElement("div")
    info.className = "gantt-row-info"
    info.innerHTML = `
            <div class="gantt-row-title task-title">└─ ${task.text}</div>
            <div class="gantt-row-details">
                <span class="responsible">${task.responsible}</span>
                <span class="status">${task.status}</span>
            </div>
        `

    const timeline = document.createElement("div")
    timeline.className = "gantt-timeline"

    if (task.start_date && task.end_date) {
      const bar = this.createGanttBar(task)
      timeline.appendChild(bar)
    }

    row.appendChild(info)
    row.appendChild(timeline)

    return row
  }

  createGanttBar(item) {
    const bar = document.createElement("div")
    bar.className = `gantt-bar ${item.type === "project" ? "project-bar" : "task-bar"}`
    bar.dataset.id = item.id

    // Calculate position and width based on dates
    const startDate = new Date(item.start_date)
    const endDate = new Date(item.end_date)
    const duration = (endDate - startDate) / (1000 * 60 * 60 * 24)

    bar.style.width = `${Math.max(duration * 20, 20)}px` // 20px per day minimum
    bar.style.left = `${this.calculateDatePosition(startDate)}px`

    // Progress indicator
    const progress = document.createElement("div")
    progress.className = "gantt-progress"
    progress.style.width = `${(item.progress || 0) * 100}%`
    bar.appendChild(progress)

    // Bar label
    const label = document.createElement("div")
    label.className = "gantt-bar-label"
    label.textContent = `${Math.round((item.progress || 0) * 100)}%`
    bar.appendChild(label)

    return bar
  }

  calculateDatePosition(date) {
    // Simple calculation - in real implementation, this would be more sophisticated
    const baseDate = new Date("2024-01-01")
    const daysDiff = (date - baseDate) / (1000 * 60 * 60 * 24)
    return Math.max(daysDiff * 20, 0)
  }

  addInteractivity() {
    const bars = this.ganttRef.el.querySelectorAll(".gantt-bar")

    bars.forEach((bar) => {
      bar.addEventListener("click", (e) => {
        this.onBarClick(e.target.dataset.id)
      })

      bar.addEventListener("mouseenter", (e) => {
        this.showTooltip(e)
      })

      bar.addEventListener("mouseleave", (e) => {
        this.hideTooltip()
      })
    })
  }

  onBarClick(itemId) {
    // Handle bar click - could open edit dialog
    console.log("Clicked on:", itemId)
  }

  showTooltip(event) {
    const tooltip = document.createElement("div")
    tooltip.className = "gantt-tooltip"
    tooltip.innerHTML = `
            <div>Click to edit</div>
            <div>Drag to reschedule</div>
        `

    document.body.appendChild(tooltip)

    const rect = event.target.getBoundingClientRect()
    tooltip.style.left = `${rect.left}px`
    tooltip.style.top = `${rect.top - tooltip.offsetHeight - 5}px`
  }

  hideTooltip() {
    const tooltip = document.querySelector(".gantt-tooltip")
    if (tooltip) {
      tooltip.remove()
    }
  }

  async exportToPDF() {
    window.open("/gantt/export/pdf", "_blank")
  }

  async exportToCSV() {
    window.open("/gantt/export/csv", "_blank")
  }
}

GanttChartWidget.template = "gantt_textile.GanttChartTemplate"

registry.category("fields").add("gantt_chart", GanttChartWidget)
