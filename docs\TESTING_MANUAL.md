# 📖 Manuel de Test - Module Gantt Textile

## 🎯 Tests Manuels dans Odoo

### 1. Tests de Base
\`\`\`bash
# Démarrer Odoo en mode développement
python3 odoo-bin -d gantt_test_db --dev=all

# Installer le module
Apps > Search "GANTT" > Install
\`\`\`

### 2. Tests Fonctionnels

#### ✅ Test 1: Création de Projet Textile
1. Aller dans `Textile Gantt > Textile Projects`
2. <PERSON><PERSON><PERSON> sur `Create`
3. Remplir:
   - Name: "Projet Coton Bio"
   - Textile Type: "Cotton"
   - Production Capacity: 500
   - Quality Grade: "A"
4. Sauvegarder
5. **Résultat attendu**: Projet créé avec champs textile visibles

#### ✅ Test 2: Création de Tâches
1. Dans le projet créé, aller à l'onglet `Tasks`
2. Créer une tâche:
   - Name: "Filage"
   - Textile Process: "Spinning"
   - Start Date: Aujourd'hui
   - End Date: Dans 5 jours
   - Progress: 25%
3. **Résultat attendu**: Tâche créée avec processus textile

#### ✅ Test 3: Visualisation Gantt
1. Aller dans `Textile Gantt > Gantt Chart`
2. Vérifier l'affichage du projet et des tâches
3. **Résultat attendu**: Barres Gantt visibles avec progression

#### ✅ Test 4: Gantt Interactif
1. Aller dans `Textile Gantt > Interactive Gantt`
2. Vérifier l'affichage interactif
3. Tester les boutons d'export
4. **Résultat attendu**: Interface interactive fonctionnelle

#### ✅ Test 5: Export PDF
1. Dans la vue Gantt, cliquer sur `Export PDF`
2. **Résultat attendu**: PDF téléchargé avec données projet

#### ✅ Test 6: Export CSV
1. Dans la vue Gantt, cliquer sur `Export CSV`
2. **Résultat attendu**: Fichier CSV téléchargé

### 3. Tests de Performance
\`\`\`bash
# Créer 100 projets de test
python3 scripts/create_test_data.py --projects 100 --tasks 500

# Tester la performance du Gantt
# Mesurer le temps de chargement
\`\`\`

### 4. Tests de Régression
- Vérifier que les fonctionnalités Odoo existantes fonctionnent
- Tester l'intégration avec le module Project standard
- Vérifier les permissions utilisateur

## 🐛 Résolution de Problèmes

### Problème: Module non visible
**Solution**: Vérifier le manifest.py et redémarrer Odoo

### Problème: Erreur JavaScript
**Solution**: Vérifier la console navigateur (F12)

### Problème: Export PDF échoue
**Solution**: Vérifier l'installation de reportlab
